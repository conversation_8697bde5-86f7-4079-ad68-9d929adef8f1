import { PermissionEnum } from '@/shared/types/permission';

/**
 * Trạng thái người dùng
 */
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  DELETED = 'deleted',
}

/**
 * Thông tin người dùng
 */
export interface User {
  id: string;
  email: string;
  fullName: string;
  avatar?: string;
  role: string;
  status: UserStatus;
  permissions?: PermissionEnum[];
}

/**
 * Thông tin xác thực tài khoản
 */
export interface VerifyAccountInfo {
  platform: string;
  value: string;
}

/**
 * Y<PERSON>u cầu đăng nhập
 */
export interface LoginRequest {
  email: string;
  password: string;
  recaptchaToken?: string;
}

/**
 * Phản hồi đăng nhập
 */
export interface LoginResponse {
  accessToken?: string;
  expiresIn: number;
  user?: User;
  info?: VerifyAccountInfo[];
  verifyToken?: string;
}

/**
 * <PERSON><PERSON><PERSON> cầu đăng ký
 */
export interface RegisterRequest {
  fullName: string;
  email: string;
  password: string;
  phoneNumber: string;
  recaptchaToken?: string;
}

/**
 * Phản hồi đăng ký
 */
export interface RegisterResponse {
  otpToken: string;
  expiresAt: number; // Thời điểm hết hạn (timestamp)
  maskedEmail?: string; // Email đã được che một phần
  otp?: string; // Mã OTP (chỉ có trong môi trường development)
  info?: VerifyAccountInfo[]; // Thông tin xác thực
}

/**
 * Yêu cầu xác thực OTP
 */
export interface VerifyOtpRequest {
  otpToken: string;
  otp: string;
  platform?: string; // Làm cho trường này trở thành optional
}

/**
 * Phản hồi xác thực OTP
 */
export interface VerifyOtpResponse {
  accessToken: string;
  expiresIn: number;
  user: User;
}

/**
 * Yêu cầu gửi lại OTP
 */
export interface ResendOtpRequest {
  otpToken: string;
  platform?: string; // Làm cho trường này trở thành optional vì backend không cần
}

/**
 * Phản hồi gửi lại OTP
 */
export interface ResendOtpResponse {
  message: string;
  otpToken?: string;
  expiresAt?: number; // Thời điểm hết hạn (timestamp)
  maskedEmail?: string; // Email đã được che một phần
  otp?: string; // Mã OTP (chỉ có trong môi trường development)
}

/**
 * Yêu cầu quên mật khẩu
 */
export interface ForgotPasswordRequest {
  email: string;
}

/**
 * Phản hồi quên mật khẩu
 */
export interface ForgotPasswordResponse {
  otpToken: string;
  expiresAt: number; // Thời điểm hết hạn (timestamp)
  maskedEmail: string; // Email đã được che một phần
  otp?: string; // Mã OTP (chỉ có trong môi trường development)
}

/**
 * Yêu cầu xác thực quên mật khẩu
 */
export interface VerifyForgotPasswordRequest {
  otpToken: string;
  otp: string;
}

/**
 * Phản hồi xác thực quên mật khẩu
 */
export interface VerifyForgotPasswordResponse {
  changePasswordToken: string;
  expiresAt: number; // Thời điểm hết hạn (timestamp)
}

/**
 * Yêu cầu đặt lại mật khẩu
 */
export interface ResetPasswordRequest {
  newPassword: string;
  changePasswordToken: string;
}

/**
 * Phản hồi đặt lại mật khẩu
 */
export interface ResetPasswordResponse {
  success: boolean;
}

/**
 * Yêu cầu làm mới token
 */
export interface RefreshTokenRequest {
  refreshToken?: string;
}

/**
 * Phản hồi làm mới token
 */
export interface RefreshTokenResponse {
  accessToken: string;
  expiresIn: number;
}

/**
 * Yêu cầu đăng nhập Google
 */
export interface GoogleAuthRequest {
  code: string;
  redirectUri?: string;
}

/**
 * Yêu cầu đăng nhập Facebook
 */
export interface FacebookAuthRequest {
  code: string;
  redirectUri?: string;
}

/**
 * Yêu cầu đăng ký công ty
 */
export interface CompanyRegisterRequest {
  companyName: string;
  companyEmail: string;
  password: string;
  phoneNumber?: string;
  recaptchaToken?: string;
}

/**
 * Phản hồi đăng ký công ty
 */
export interface CompanyRegisterResponse {
  message: string;
  otpToken: string;
  expiresAt: number;
  maskedEmail?: string;
  otp?: string;
}

/**
 * Yêu cầu xác thực email công ty
 */
export interface CompanyVerifyEmailRequest {
  token: string;
  otp: string;
}

/**
 * Phản hồi xác thực email công ty
 */
export interface CompanyVerifyEmailResponse {
  message: string;
  accessToken?: string;
  company?: Company;
}

/**
 * Yêu cầu đăng nhập công ty
 */
export interface CompanyLoginRequest {
  email: string;
  password: string;
  recaptchaToken?: string;
}

/**
 * Thông tin công ty
 */
export interface Company {
  id: number;
  companyName: string;
  subdomain: string;
  taxCode: string;
  companyEmail: string;
  phoneNumber: string | null;
  address: string | null;
  status: CompanyStatus;
  createdAt: number;
  updatedAt: number | null;
}

/**
 * Trạng thái công ty
 */
export enum CompanyStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
}

/**
 * Phản hồi đăng nhập công ty
 */
export interface CompanyLoginResponse {
  accessToken: string;
  company: Company;
}

/**
 * Auth state interface
 */
export interface AuthState {
  user: User | null;
  accessToken: string | null;
  expiresIn: number | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Saved credentials interface
 */
export interface SavedCredentials {
  username: string;
  encryptedPassword: string; // Mật khẩu đã được mã hóa
  timestamp: number;
}

/**
 * Admin login request
 */
export interface AdminLoginRequest extends LoginRequest {
  role: string;
}

/**
 * Admin forgot password request
 */
export interface AdminForgotPasswordRequest extends ForgotPasswordRequest {
  role: string;
}

/**
 * Admin verify forgot password request
 */
export interface AdminVerifyForgotPasswordRequest {
  token: string;
  code: string;
  role: string;
}

/**
 * Admin reset password request
 */
export interface AdminResetPasswordRequest {
  token: string;
  password: string;
  role: string;
}

/**
 * Admin resend OTP request
 */
export interface AdminResendOtpRequest {
  email: string;
  role: string;
}

/**
 * Yêu cầu xác thực hai lớp
 */
export interface VerifyTwoFactorRequest {
  verifyToken: string;
  method: string;
  code: string;
}

/**
 * Phản hồi xác thực hai lớp
 */
export interface VerifyTwoFactorResponse {
  accessToken: string;
  expiresIn: number;
  user: User;
}
