import { apiClient } from '@/shared/api';
import {
  Company,
  CompanyLoginRequest,
  CompanyLoginResponse,
  CompanyRegisterRequest,
  CompanyRegisterResponse,
  CompanyVerifyEmailRequest,
  CompanyVerifyEmailResponse,
} from '../types/company-auth.types';

/**
 * Service xử lý các API liên quan đến xác thực công ty
 */
export const CompanyAuthService = {
  /**
   * Đăng ký tài khoản công ty mới
   * @param data Thông tin đăng ký
   * @returns Kết quả đăng ký
   */
  register: (data: CompanyRegisterRequest) => {
    return apiClient.post<CompanyRegisterResponse>('/auth/company/register', data);
  },

  /**
   * Xác thực email đăng ký
   * @param data Thông tin xác thực
   * @returns Kết quả xác thực
   */
  verifyEmail: (data: CompanyVerifyEmailRequest) => {
    return apiClient.post<CompanyVerifyEmailResponse>('/auth/company/verify-email', data);
  },

  /**
   * Đăng nhập tài khoản công ty
   * @param data Thông tin đăng nhập
   * @returns Kết quả đăng nhập
   */
  login: (data: CompanyLoginRequest) => {
    return apiClient.post<CompanyLoginResponse>('/auth/company/login', data);
  },

  /**
   * Lấy thông tin công ty hiện tại
   * @returns Thông tin công ty
   */
  getProfile: () => {
    return apiClient.get<Company>('/auth/company/profile');
  },
};
