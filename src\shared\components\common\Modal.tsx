import { ReactNode, useCallback, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';

import { Button } from './index';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
  footer?: ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closeOnClickOutside?: boolean;
  closeOnEsc?: boolean;
  className?: string;
}

const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  footer,
  size = 'md',
  closeOnClickOutside = true,
  closeOnEsc = true,
  className = '',
}: ModalProps) => {
  const { t } = useTranslation();
  const modalRef = useRef<HTMLDivElement>(null);

  // Size classes
  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
  };

  // Handle click outside
  const handleClickOutside = useCallback(
    (e: MouseEvent) => {
      if (closeOnClickOutside && modalRef.current && !modalRef.current.contains(e.target as Node)) {
        onClose();
      }
    },
    [closeOnClickOutside, onClose]
  );

  // Handle escape key
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (closeOnEsc && e.key === 'Escape') {
        onClose();
      }
    },
    [closeOnEsc, onClose]
  );

  // Add event listeners
  useEffect(() => {
    if (isOpen) {
      const currentHandleClickOutside = handleClickOutside;
      const currentHandleKeyDown = handleKeyDown;

      document.addEventListener('mousedown', currentHandleClickOutside);
      document.addEventListener('keydown', currentHandleKeyDown);
      document.body.style.overflow = 'hidden'; // Prevent scrolling when modal is open

      return () => {
        document.removeEventListener('mousedown', currentHandleClickOutside);
        document.removeEventListener('keydown', currentHandleKeyDown);
        document.body.style.overflow = ''; // Restore scrolling when modal is closed
      };
    }
    return undefined;
  }, [isOpen, handleClickOutside, handleKeyDown]);

  // Don't render if not open
  if (!isOpen) {
    return null;
  }

  // Create portal to render modal at the end of the document body
  return createPortal(
    <div className="fixed inset-0 z-[9500] flex items-center justify-center p-4 bg-black bg-opacity-50 animate-fade-in">
      <div
        ref={modalRef}
        className={`bg-white dark:bg-dark-light rounded shadow-lg ${sizeClasses[size]} w-full animate-slide-in ${className}`}
      >
        {title && (
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold">{title}</h3>
            <button
              onClick={onClose}
              className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-dark-lighter"
              aria-label="Close"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        )}

        <div className="p-4">{children}</div>

        {/* Chỉ hiển thị footer nếu footer được truyền vào và không phải null */}
        {footer !== undefined && footer !== null && (
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">{footer}</div>
        )}

        {/* Hiển thị footer mặc định nếu footer là undefined */}
        {footer === undefined && (
          <div className="flex justify-end p-4 border-t border-gray-200 dark:border-gray-700">
            <Button variant="outline" onClick={onClose} className="mr-2">
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button variant="primary" onClick={onClose}>
              {t('common.ok', 'OK')}
            </Button>
          </div>
        )}
      </div>
    </div>,
    document.body
  );
};

export default Modal;
