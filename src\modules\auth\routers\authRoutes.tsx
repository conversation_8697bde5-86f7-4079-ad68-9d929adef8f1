import { Loading } from '@/shared/components';
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import AuthLayout from '../../../shared/layouts/AuthLayout';

// Import User Auth pages with lazy loading
const LoginPage = lazy(() => import('@/modules/auth/pages/LoginPage'));
const VerifyEmailPage = lazy(() => import('@/modules/auth/pages/VerifyEmailPage'));

/**
 * Auth module routes
 */
const authRoutes: RouteObject[] = [
  // User Auth Routes
  {
    path: '/auth',
    element: (
      <AuthLayout title="Authentication">
        <Suspense fallback={<Loading />}>
          <LoginPage />
        </Suspense>
      </AuthLayout>
    ),
  },
  {
    path: '/auth/verify-email',
    element: (
      <AuthLayout title="Verify Email">
        <Suspense fallback={<Loading />}>
          <VerifyEmailPage />
        </Suspense>
      </AuthLayout>
    ),
  },
];

export default authRoutes;
