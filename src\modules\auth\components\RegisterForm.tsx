import React, { useState } from 'react';
import { FieldValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Button, Form, FormGrid, FormItem, Input } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { useFormErrors } from '@/shared/hooks';

import { PasswordInput } from '@/shared/components/common/PasswordInput';
import { useCompany } from '../hooks/useCompany';
import { useCompanyAuth } from '../hooks/useCompanyAuth';
import { CompanyRegisterFormValues, createCompanyRegisterSchema } from '../schemas/auth.schema';
import { CompanyRegisterResponse } from '../types/company-auth.types';
import RecaptchaModal from './RecaptchaModal';

interface RegisterFormProps {
  onSuccess?: () => void;
}

/**
 * Register form component
 */
const RegisterForm: React.FC<RegisterFormProps> = ({ onSuccess }) => {
  const { t } = useTranslation(['auth', 'validation']);
  const navigate = useNavigate();
  const { registerCompany } = useCompanyAuth();
  const { formRef, setFormErrors } = useFormErrors<CompanyRegisterFormValues>();
  const { saveRegisterInfo } = useCompany();

  // State cho modal reCAPTCHA
  const [showRecaptchaModal, setShowRecaptchaModal] = useState(false);
  const [pendingRegisterData, setPendingRegisterData] = useState<CompanyRegisterFormValues | null>(
    null
  );

  // Create company register schema with translations
  const registerSchema = createCompanyRegisterSchema(t);

  // Handle form submission
  const handleSubmit = (values: unknown) => {
    // Use type assertion with a specific type instead of 'any'
    const registerValues = values as CompanyRegisterFormValues;

    console.log('Form submitted with data:', registerValues);

    // Reset form errors
    setFormErrors({});

    // Lưu dữ liệu form và hiển thị modal reCAPTCHA
    setPendingRegisterData(registerValues);
    setShowRecaptchaModal(true);
  };

  // Xử lý khi reCAPTCHA thành công
  const handleRecaptchaSuccess = (token: string) => {
    if (!pendingRegisterData) return;

    // Call register API với token reCAPTCHA
    registerCompany.mutate(
      {
        companyName: pendingRegisterData.companyName,
        companyEmail: pendingRegisterData.companyEmail,
        password: pendingRegisterData.password,
        phoneNumber: pendingRegisterData.phoneNumber,
        recaptchaToken: token,
        // Không gửi taxCode và address vì backend API không yêu cầu
      },
      {
        onSuccess: (response: ApiResponseDto<CompanyRegisterResponse>) => {
          console.log('Registration successful:', response);

          // Lưu thông tin đăng ký vào Redux
          if (response.code === 201 && response.result) {
            saveRegisterInfo({
              otpToken: response.result.otpToken,
              expiresAt: response.result.expiresAt,
              maskedEmail: response.result.maskedEmail,
              otp: response.result.otp,
              message: response.result.message,
            });
          }

          // Reset modal state sau khi đăng ký thành công
          setShowRecaptchaModal(false);
          setPendingRegisterData(null);

          // Gọi callback onSuccess nếu có
          if (onSuccess) {
            onSuccess();
          }

          // Chuyển hướng đến trang xác thực email
          navigate('/auth/verify-email');
        },
        onError: (error: unknown) => {
          console.error('Registration error:', error);

          // Lấy thông báo lỗi từ response API
          const errorMsg = t('registerError', 'Đăng ký thất bại');

          // Kiểm tra xem error có phải là AxiosError không
          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as {
              response: {
                data?: {
                  code?: number;
                  message?: string;
                  errors?: Record<string, string>;
                };
              };
            };

            // Kiểm tra mã lỗi 10008 (email đã tồn tại)
            if (axiosError.response.data?.code === 10008) {
              // Hiển thị lỗi email đã tồn tại
              setFormErrors({
                companyEmail: t(
                  'emailAlreadyExists',
                  'Email này đã được đăng ký. Vui lòng sử dụng email khác.'
                ),
              });
            }
            // Nếu có lỗi cụ thể cho từng field
            else if (axiosError.response.data?.errors) {
              // Đặt lỗi cho các field tương ứng
              setFormErrors(axiosError.response.data.errors);
            } else if (axiosError.response.data?.message) {
              // Nếu chỉ có thông báo lỗi chung
              setFormErrors({
                general: axiosError.response.data.message,
              });
            }
          } else {
            // Nếu không có lỗi cụ thể
            setFormErrors({
              general: errorMsg,
            });
          }

          // Reset modal state sau khi đăng ký thất bại
          setShowRecaptchaModal(false);
          setPendingRegisterData(null);
        },
      }
    );
  };

  // Xử lý đóng modal reCAPTCHA
  const handleRecaptchaClose = () => {
    setShowRecaptchaModal(false);
    setPendingRegisterData(null);
  };

  return (
    <Form
      ref={formRef as unknown as React.RefObject<FormRef<FieldValues>>}
      schema={registerSchema}
      onSubmit={handleSubmit}
      className="space-y-6"
      autoComplete="off"
    >
      <FormItem name="companyName" label={t('companyName')} required>
        <Input fullWidth autoComplete="off" />
      </FormItem>

      <FormItem name="companyEmail" label={t('email')} required>
        <Input type="email" fullWidth autoComplete="off" />
      </FormItem>

      <FormItem name="phoneNumber" label={t('phoneNumber')} required>
        <Input type="tel" fullWidth autoComplete="off" />
      </FormItem>

      <FormGrid columns={2}>
        <FormItem name="password" label={t('password')} required>
          <PasswordInput fullWidth autoComplete="new-password" />
        </FormItem>

        <FormItem name="confirmPassword" label={t('confirmPassword')} required>
          <PasswordInput fullWidth autoComplete="new-password" />
        </FormItem>
      </FormGrid>

      <Button type="submit" variant="primary" fullWidth isLoading={registerCompany.isPending}>
        {t('signUp')}
      </Button>

      {/* Modal reCAPTCHA */}
      <RecaptchaModal
        isOpen={showRecaptchaModal}
        onClose={handleRecaptchaClose}
        onSuccess={handleRecaptchaSuccess}
      />
    </Form>
  );
};

export default RegisterForm;
